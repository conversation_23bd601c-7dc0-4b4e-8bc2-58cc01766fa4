// components/BasicCenterCTA.js
import React from "react";
import styles from "./style.module.scss";
import Rounded from "../../../common/RoundedButton";
import { useTranslation } from "@/hooks/useTranslation";
import { useParams } from "next/navigation";
import PropTypes from "prop-types";

export default function BasicCenterCTA({
  backgroundColor = "#19271B",
  titleColor = "#ffffff",
  textColor = "#C8CEC9",
  buttonColor = "#FFA3B2",
  maskPosition = "top", // "top", "bottom", "both", "none"
  title = null,
  text = null,
  buttonText = null,
  buttonHref = null,
  className = "",
  containerSize = "medium", // "small", "medium", "large"
  textAlign = "center", // "left", "center", "right"
  customContent = null, // Pour du contenu personnalisé
}) {
  const { t } = useTranslation('common');
  const params = useParams();
  const locale = params?.locale || 'fr';

  const containerStyle = {
    backgroundColor,
    textAlign: textAlign === "center" ? "center" : textAlign,
  };

  // Styles pour les masques (même couleur que le background)
  const maskStyle = {
    backgroundColor,
  };

  // Contenu par défaut ou personnalisé
  const displayTitle = title || t('cta.general_questions_title');
  const displayText = text || t('cta.general_questions_text');
  const displayButtonText = buttonText || t('cta.view_faq');
  const displayButtonHref = buttonHref || `/${locale}/questions-frequentes`;

  return (
    <div
      className={`${styles.basicCenterCTA} ${className}`}
      style={containerStyle}
      data-mask-position={maskPosition}
    >
      {/* Masque supérieur */}
      {(maskPosition === "top" || maskPosition === "both") && (
        <div
          className={`${styles.mask} ${styles.maskTop}`}
          style={maskStyle}
        />
      )}

      <div className={`container ${containerSize} ${styles.container}`}>
        {customContent ? (
          customContent
        ) : (
          <>
            <h2
              className={styles.title}
              style={{ color: titleColor }}
            >
              {displayTitle}
            </h2>
            <p
              className={styles.text}
              style={{ color: textColor }}
            >
              {displayText}
            </p>
            <div className={styles.buttonWrapper}>
              <Rounded
                href={displayButtonHref}
                backgroundColor={buttonColor}
              >
                <p>{displayButtonText}</p>
              </Rounded>
            </div>
          </>
        )}
      </div>

      {/* Masque inférieur */}
      {(maskPosition === "bottom" || maskPosition === "both") && (
        <div
          className={`${styles.mask} ${styles.maskBottom}`}
          style={maskStyle}
        />
      )}
    </div>
  );
}

BasicCenterCTA.propTypes = {
  backgroundColor: PropTypes.string,
  titleColor: PropTypes.string,
  textColor: PropTypes.string,
  buttonColor: PropTypes.string,
  maskPosition: PropTypes.oneOf(["top", "bottom", "both", "none"]),
  title: PropTypes.string,
  text: PropTypes.string,
  buttonText: PropTypes.string,
  buttonHref: PropTypes.string,
  className: PropTypes.string,
  containerSize: PropTypes.oneOf(["small", "medium", "large"]),
  textAlign: PropTypes.oneOf(["left", "center", "right"]),
  customContent: PropTypes.node,
};
