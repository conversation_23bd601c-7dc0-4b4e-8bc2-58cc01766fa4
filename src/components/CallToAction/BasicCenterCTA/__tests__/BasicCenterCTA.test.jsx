import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import BasicCenterCTA from '../index';

// Mock des hooks Next.js
jest.mock('next/navigation', () => ({
  useParams: () => ({ locale: 'fr' }),
}));

// Mock du hook de traduction
jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        'cta.general_questions_title': 'Des questions générales ?',
        'cta.general_questions_text': 'Consultez notre FAQ pour trouver des réponses.',
        'cta.view_faq': 'Voir la FAQ',
      };
      return translations[key] || key;
    },
  }),
}));

describe('BasicCenterCTA', () => {
  test('renders with default props', () => {
    render(<BasicCenterCTA />);
    
    expect(screen.getByText('Des questions générales ?')).toBeInTheDocument();
    expect(screen.getByText('Consultez notre FAQ pour trouver des réponses.')).toBeInTheDocument();
    expect(screen.getByText('Voir la FAQ')).toBeInTheDocument();
  });

  test('renders with custom colors', () => {
    render(
      <BasicCenterCTA 
        backgroundColor="#FF0000"
        titleColor="#00FF00"
        textColor="#0000FF"
      />
    );
    
    const container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveStyle('background-color: #FF0000');
    
    const title = screen.getByText('Des questions générales ?');
    expect(title).toHaveStyle('color: #00FF00');
    
    const text = screen.getByText('Consultez notre FAQ pour trouver des réponses.');
    expect(text).toHaveStyle('color: #0000FF');
  });

  test('renders with custom content', () => {
    const customContent = <div data-testid="custom-content">Contenu personnalisé</div>;
    
    render(<BasicCenterCTA customContent={customContent} />);
    
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Contenu personnalisé')).toBeInTheDocument();
    
    // Le contenu par défaut ne devrait pas être présent
    expect(screen.queryByText('Des questions générales ?')).not.toBeInTheDocument();
  });

  test('renders with custom text content', () => {
    render(
      <BasicCenterCTA 
        title="Titre personnalisé"
        text="Texte personnalisé"
        buttonText="Bouton personnalisé"
      />
    );
    
    expect(screen.getByText('Titre personnalisé')).toBeInTheDocument();
    expect(screen.getByText('Texte personnalisé')).toBeInTheDocument();
    expect(screen.getByText('Bouton personnalisé')).toBeInTheDocument();
  });

  test('renders with different mask positions', () => {
    const { rerender } = render(<BasicCenterCTA maskPosition="top" />);
    
    let container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveAttribute('data-mask-position', 'top');
    expect(container.querySelector('.maskTop')).toBeInTheDocument();
    expect(container.querySelector('.maskBottom')).not.toBeInTheDocument();

    rerender(<BasicCenterCTA maskPosition="bottom" />);
    container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveAttribute('data-mask-position', 'bottom');
    expect(container.querySelector('.maskTop')).not.toBeInTheDocument();
    expect(container.querySelector('.maskBottom')).toBeInTheDocument();

    rerender(<BasicCenterCTA maskPosition="both" />);
    container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveAttribute('data-mask-position', 'both');
    expect(container.querySelector('.maskTop')).toBeInTheDocument();
    expect(container.querySelector('.maskBottom')).toBeInTheDocument();

    rerender(<BasicCenterCTA maskPosition="none" />);
    container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveAttribute('data-mask-position', 'none');
    expect(container.querySelector('.maskTop')).not.toBeInTheDocument();
    expect(container.querySelector('.maskBottom')).not.toBeInTheDocument();
  });

  test('applies custom className', () => {
    render(<BasicCenterCTA className="custom-class" />);
    
    const container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveClass('custom-class');
  });

  test('renders with different container sizes', () => {
    const { rerender } = render(<BasicCenterCTA containerSize="small" />);
    
    let containerDiv = screen.getByText('Des questions générales ?').closest('.container');
    expect(containerDiv).toHaveClass('small');

    rerender(<BasicCenterCTA containerSize="medium" />);
    containerDiv = screen.getByText('Des questions générales ?').closest('.container');
    expect(containerDiv).toHaveClass('medium');

    rerender(<BasicCenterCTA containerSize="large" />);
    containerDiv = screen.getByText('Des questions générales ?').closest('.container');
    expect(containerDiv).toHaveClass('large');
  });

  test('applies text alignment styles', () => {
    const { rerender } = render(<BasicCenterCTA textAlign="left" />);
    
    let container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveStyle('text-align: left');

    rerender(<BasicCenterCTA textAlign="center" />);
    container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveStyle('text-align: center');

    rerender(<BasicCenterCTA textAlign="right" />);
    container = screen.getByText('Des questions générales ?').closest('.basicCenterCTA');
    expect(container).toHaveStyle('text-align: right');
  });

  test('uses maskColor when provided', () => {
    render(<BasicCenterCTA maskPosition="top" maskColor="#FFFF00" />);
    
    const mask = document.querySelector('.maskTop');
    expect(mask).toHaveStyle('background-color: #FFFF00');
  });

  test('falls back to backgroundColor for mask when maskColor is not provided', () => {
    render(<BasicCenterCTA maskPosition="top" backgroundColor="#FF0000" />);
    
    const mask = document.querySelector('.maskTop');
    expect(mask).toHaveStyle('background-color: #FF0000');
  });
});
