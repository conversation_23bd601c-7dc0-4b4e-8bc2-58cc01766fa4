// Exemples d'utilisation du composant BasicCenterCTA
import React from 'react';
import BasicCenterCTA from './index';

export function ExamplesBasicCenterCTA() {
  return (
    <div style={{ padding: '2rem', backgroundColor: '#f5f5f5' }}>
      <h1>Exemples BasicCenterCTA</h1>
      
      {/* Exemple 1: Utilisation par défaut */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>1. Utilisation par défaut</h2>
        <BasicCenterCTA />
      </section>

      {/* Exemple 2: Couleurs personnalisées */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>2. Couleurs personnalisées (bleu)</h2>
        <BasicCenterCTA 
          backgroundColor="#1E40AF"
          titleColor="#FFFFFF"
          textColor="#DBEAFE"
          buttonColor="#FBBF24"
        />
      </section>

      {/* Exemple 3: Masque en dessous */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>3. Masque en dessous (violet)</h2>
        <BasicCenterCTA 
          backgroundColor="#7C3AED"
          maskPosition="bottom"
          titleColor="#F3E8FF"
          textColor="#DDD6FE"
          buttonColor="#10B981"
        />
      </section>

      {/* Exemple 4: Les deux masques */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>4. Masques dessus et dessous (rouge)</h2>
        <BasicCenterCTA 
          backgroundColor="#DC2626"
          maskPosition="both"
          maskColor="#991B1B"
          titleColor="#FFFFFF"
          textColor="#FECACA"
          buttonColor="#FBBF24"
        />
      </section>

      {/* Exemple 5: Sans masque */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>5. Sans masque (vert)</h2>
        <BasicCenterCTA 
          backgroundColor="#059669"
          maskPosition="none"
          titleColor="#FFFFFF"
          textColor="#A7F3D0"
          buttonColor="#F59E0B"
        />
      </section>

      {/* Exemple 6: Contenu personnalisé */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>6. Contenu personnalisé</h2>
        <BasicCenterCTA 
          backgroundColor="#1F2937"
          customContent={
            <div style={{ textAlign: 'center' }}>
              <h2 style={{ color: '#F9FAFB', marginBottom: '1rem' }}>
                🚀 Lancez votre projet
              </h2>
              <p style={{ color: '#D1D5DB', marginBottom: '2rem' }}>
                Transformez vos idées en réalité avec notre équipe d'experts
              </p>
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                <button style={{ 
                  padding: '0.75rem 1.5rem', 
                  backgroundColor: '#3B82F6', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '0.5rem',
                  cursor: 'pointer'
                }}>
                  Démarrer maintenant
                </button>
                <button style={{ 
                  padding: '0.75rem 1.5rem', 
                  backgroundColor: 'transparent', 
                  color: '#D1D5DB', 
                  border: '1px solid #6B7280', 
                  borderRadius: '0.5rem',
                  cursor: 'pointer'
                }}>
                  En savoir plus
                </button>
              </div>
            </div>
          }
        />
      </section>

      {/* Exemple 7: Texte aligné à gauche */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>7. Alignement à gauche, grand conteneur</h2>
        <BasicCenterCTA 
          backgroundColor="#374151"
          textAlign="left"
          containerSize="large"
          title="Notre expertise technique"
          text="Nous maîtrisons les dernières technologies pour créer des solutions innovantes et performantes adaptées à vos besoins spécifiques."
          buttonText="Découvrir nos services"
          buttonHref="/services"
          titleColor="#F9FAFB"
          textColor="#D1D5DB"
          buttonColor="#EF4444"
        />
      </section>

      {/* Exemple 8: Style moderne avec dégradé simulé */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>8. Style moderne (orange)</h2>
        <BasicCenterCTA 
          backgroundColor="#EA580C"
          maskPosition="top"
          maskColor="#C2410C"
          titleColor="#FFFFFF"
          textColor="#FED7AA"
          buttonColor="#1F2937"
          title="Innovation & Créativité"
          text="Nous repoussons les limites du possible pour créer des expériences utilisateur exceptionnelles."
          buttonText="Voir nos réalisations"
          className="cta-moderne"
        />
      </section>

      {/* Exemple 9: Thème sombre élégant */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>9. Thème sombre élégant</h2>
        <BasicCenterCTA 
          backgroundColor="#0F172A"
          maskPosition="both"
          maskColor="#1E293B"
          titleColor="#F1F5F9"
          textColor="#94A3B8"
          buttonColor="#0EA5E9"
          containerSize="medium"
          title="Excellence & Performance"
          text="Des solutions techniques de haute qualité pour propulser votre entreprise vers le succès."
          buttonText="Commencer"
        />
      </section>

      {/* Exemple 10: Couleurs vives et modernes */}
      <section style={{ marginBottom: '4rem' }}>
        <h2>10. Couleurs vives et modernes</h2>
        <BasicCenterCTA 
          backgroundColor="#BE185D"
          maskPosition="top"
          titleColor="#FECACA"
          textColor="#F9A8D4"
          buttonColor="#FACC15"
          title="Créativité sans limites"
          text="Explorez de nouveaux horizons avec nos solutions créatives et innovantes."
          buttonText="Explorer"
        />
      </section>
    </div>
  );
}

export default ExamplesBasicCenterCTA;
