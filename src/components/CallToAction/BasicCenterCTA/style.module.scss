.basicCenterCTA {
  position: relative;
  margin-top: var(--section-padding);
  margin-bottom: var(--section-padding);
  background-color: #19271B;
  transition: background-color 0.3s ease;

  // Suppression de l'ancien pseudo-élément ::before
  // Les masques sont maintenant gérés par des éléments séparés

  .container {
    position: relative;
    z-index: 1;
    padding-bottom: calc(var(--section-padding) / 2);
    padding-top: calc(var(--section-padding) / 2);
  }

  .title {
    color: #fff;
    margin-bottom: calc(var(--gap-padding) / 1.5);
    font-weight: 600;
    line-height: 1.2;
    transition: color 0.3s ease;
  }

  .text {
    color: #C8CEC9;
    margin-bottom: calc(var(--gap-padding) * 2);
    line-height: 1.6;
    opacity: 0.9;
    transition: color 0.3s ease;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  // Styles pour les masques
  .mask {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 0;
    transition: background-color 0.3s ease;
  }

  .maskTop {
    top: -89px;
    height: 90px;
    clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
  }

  .maskBottom {
    bottom: -89px;
    height: 90px;
    clip-path: polygon(0% 0%, 96% 0%, 100% 80%, 100% 100%, 0% 100%);
    transform: rotate(180deg);
  }

  // Alignement du texte
  &[data-mask-position="none"] {
    margin-top: calc(var(--section-padding) / 2);
    margin-bottom: calc(var(--section-padding) / 2);
  }

  // Responsive design
  @media (min-width: 768px) {
    .maskTop {
      top: -119px;
      height: 120px;
      clip-path: polygon(0% 100%, 5% 0%, 98% 0%, 100% 25%, 100% 100%);
    }

    .maskBottom {
      bottom: -119px;
      height: 120px;
      clip-path: polygon(0% 0%, 95% 0%, 100% 75%, 100% 100%, 0% 100%);
    }
  }

  @media (min-width: 1024px) {
    .maskTop {
      top: -139px;
      height: 140px;
      clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
    }

    .maskBottom {
      bottom: -139px;
      height: 140px;
      clip-path: polygon(0% 0%, 96% 0%, 100% 80%, 100% 100%, 0% 100%);
    }
  }

  // Amélioration de l'accessibilité et de l'UX
  &:focus-within {
    outline: 2px solid rgba(255, 255, 255, 0.3);
    outline-offset: 4px;
  }

  // Animation subtile au chargement
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .container {
    animation: fadeInUp 0.6s ease-out;
  }

  // Support pour différentes tailles de conteneur
  .container.small {
    max-width: 600px;
    margin: 0 auto;
  }

  .container.medium {
    max-width: 800px;
    margin: 0 auto;
  }

  .container.large {
    max-width: 1000px;
    margin: 0 auto;
  }

  // Styles pour le bouton
  .buttonWrapper {
    display: flex;
    justify-content: center;

    @media (min-width: 768px) {
      display: inline-flex;
      width: auto;
    }
  }
}
