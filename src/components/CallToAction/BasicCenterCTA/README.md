# BasicCenterCTA Component

Un composant CTA (Call To Action) professionnel et personnalisable avec support des masques et couleurs personnalisées.

## Fonctionnalités

- ✅ Couleur de fond personnalisable (format hexadécimal)
- ✅ Position des masques configurable (dessus, dessous, les deux, aucun)
- ✅ Couleurs personnalisables pour le titre et le texte
- ✅ Couleur du bouton personnalisable
- ✅ Support de l'internationalisation
- ✅ Design responsive
- ✅ Animations subtiles
- ✅ Accessibilité améliorée
- ✅ Contenu personnalisable

## Props

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `backgroundColor` | string | `"#19271B"` | Couleur de fond en hexadécimal |
| `titleColor` | string | `"#ffffff"` | Couleur du titre en hexadécimal |
| `textColor` | string | `"#C8CEC9"` | Couleur du texte en hexadécimal |
| `buttonColor` | string | `"#FFA3B2"` | Couleur du bouton en hexadécimal |
| `maskPosition` | string | `"top"` | Position du masque: "top", "bottom", "both", "none" |
| `maskColor` | string | `null` | Couleur du masque (utilise backgroundColor si null) |
| `title` | string | `null` | Titre personnalisé (utilise la traduction si null) |
| `text` | string | `null` | Texte personnalisé (utilise la traduction si null) |
| `buttonText` | string | `null` | Texte du bouton (utilise la traduction si null) |
| `buttonHref` | string | `null` | Lien du bouton (utilise le lien par défaut si null) |
| `className` | string | `""` | Classes CSS supplémentaires |
| `containerSize` | string | `"small"` | Taille du conteneur: "small", "medium", "large" |
| `textAlign` | string | `"center"` | Alignement du texte: "left", "center", "right" |
| `customContent` | node | `null` | Contenu personnalisé (remplace le contenu par défaut) |

## Exemples d'utilisation

### Utilisation basique (avec traductions)
```jsx
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';

<BasicCenterCTA />
```

### Avec couleurs personnalisées
```jsx
<BasicCenterCTA 
  backgroundColor="#2D3748"
  titleColor="#F7FAFC"
  textColor="#E2E8F0"
  buttonColor="#4299E1"
/>
```

### Avec masque en dessous
```jsx
<BasicCenterCTA 
  backgroundColor="#1A202C"
  maskPosition="bottom"
  maskColor="#2D3748"
/>
```

### Avec les deux masques
```jsx
<BasicCenterCTA 
  backgroundColor="#553C9A"
  maskPosition="both"
  titleColor="#FBBF24"
  textColor="#FDE68A"
/>
```

### Sans masque
```jsx
<BasicCenterCTA 
  backgroundColor="#DC2626"
  maskPosition="none"
  titleColor="#FFFFFF"
/>
```

### Avec contenu personnalisé
```jsx
<BasicCenterCTA 
  backgroundColor="#059669"
  customContent={
    <div>
      <h2>Titre personnalisé</h2>
      <p>Votre contenu personnalisé ici</p>
      <button>Action personnalisée</button>
    </div>
  }
/>
```

### Avec texte aligné à gauche et grand conteneur
```jsx
<BasicCenterCTA 
  backgroundColor="#7C3AED"
  textAlign="left"
  containerSize="large"
  title="Notre expertise à votre service"
  text="Découvrez comment nous pouvons vous aider à atteindre vos objectifs."
  buttonText="En savoir plus"
  buttonHref="/expertise"
/>
```

## Styles CSS personnalisés

Vous pouvez ajouter des styles personnalisés en utilisant la prop `className` :

```jsx
<BasicCenterCTA 
  className="mon-cta-personnalise"
  backgroundColor="#1F2937"
/>
```

```scss
.mon-cta-personnalise {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  
  .container {
    padding: 4rem 2rem;
  }
}
```

## Accessibilité

Le composant inclut des améliorations d'accessibilité :
- Support du focus clavier
- Contrastes de couleurs appropriés
- Structure sémantique HTML
- Animations respectueuses des préférences utilisateur

## Responsive Design

Le composant s'adapte automatiquement aux différentes tailles d'écran :
- Mobile : masques plus petits, espacement optimisé
- Tablette : masques moyens
- Desktop : masques complets avec animations
