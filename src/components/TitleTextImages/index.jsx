import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';

export default function TitleTextImages({
  locale = 'fr',
  titleKey = 'agency.daily_life.title',
  descriptionKey = 'agency.daily_life.description',
  namespace = 'pages',
  image1Src = '/images/lucas-joliveau-siege-ordinateur-portable.png',
  image2Src = '/images/lucas-joliveau-google-meet.png',
  image1AltKey = 'agency.daily_life.image1_alt',
  image2AltKey = 'agency.daily_life.image2_alt'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.titleTextImages} section`}>
      <div className="container">
        <div className={styles.content}>
          <div className={styles.textSection}>
                  <GSAPTextReveal
                  as="h2"
                  className={styles.title}
                  {...getPreset('hero')}
                >
                  {t(titleKey)}
                </GSAPTextReveal>
            
            <GSAPTextReveal
              as="p"
              className={styles.description}
              {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
            >
              {t(descriptionKey)}
            </GSAPTextReveal>
          </div>

          <div className={styles.imagesSection}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.4 }}
            >
              <Image
                src={image1Src}
                alt={t(image1AltKey)}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>

            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.5 }}
            >
              <Image
                src={image2Src}
                alt={t(image2AltKey)}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
