"use client";

import React from 'react';
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';

export default function DemoCTAPage() {
  return (
    <main style={{ backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ padding: '2rem 0' }}>
        <div className="container">
          <h1 style={{ textAlign: 'center', marginBottom: '3rem', color: '#1f2937' }}>
            Démonstration BasicCenterCTA
          </h1>
        </div>

        {/* Exemple 1: Par défaut */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              1. Configuration par défaut
            </h2>
          </div>
          <BasicCenterCTA />
        </section>

        {/* Exemple 2: Couleurs personnalisées */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              2. Couleurs personnalisées (Bleu)
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#1E40AF"
            titleColor="#FFFFFF"
            textColor="#DBEAFE"
            buttonColor="#FBBF24"
          />
        </section>

        {/* Exemple 3: Masque en dessous */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              3. Masque en dessous (Violet)
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#7C3AED"
            maskPosition="bottom"
            titleColor="#F3E8FF"
            textColor="#DDD6FE"
            buttonColor="#10B981"
          />
        </section>

        {/* Exemple 4: Les deux masques */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              4. Masques dessus et dessous (Rouge)
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#DC2626"
            maskPosition="both"
            maskColor="#991B1B"
            titleColor="#FFFFFF"
            textColor="#FECACA"
            buttonColor="#FBBF24"
          />
        </section>

        {/* Exemple 5: Sans masque */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              5. Sans masque (Vert)
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#059669"
            maskPosition="none"
            titleColor="#FFFFFF"
            textColor="#A7F3D0"
            buttonColor="#F59E0B"
          />
        </section>

        {/* Exemple 6: Contenu personnalisé */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              6. Contenu personnalisé
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#1F2937"
            customContent={
              <div style={{ textAlign: 'center' }}>
                <h2 style={{ color: '#F9FAFB', marginBottom: '1rem', fontSize: '2rem' }}>
                  🚀 Lancez votre projet
                </h2>
                <p style={{ color: '#D1D5DB', marginBottom: '2rem', fontSize: '1.1rem' }}>
                  Transformez vos idées en réalité avec notre équipe d'experts
                </p>
                <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                  <button style={{ 
                    padding: '0.75rem 1.5rem', 
                    backgroundColor: '#3B82F6', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '0.5rem',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: '500'
                  }}>
                    Démarrer maintenant
                  </button>
                  <button style={{ 
                    padding: '0.75rem 1.5rem', 
                    backgroundColor: 'transparent', 
                    color: '#D1D5DB', 
                    border: '1px solid #6B7280', 
                    borderRadius: '0.5rem',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: '500'
                  }}>
                    En savoir plus
                  </button>
                </div>
              </div>
            }
          />
        </section>

        {/* Exemple 7: Texte aligné à gauche */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              7. Alignement à gauche, grand conteneur
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#374151"
            textAlign="left"
            containerSize="large"
            title="Notre expertise technique"
            text="Nous maîtrisons les dernières technologies pour créer des solutions innovantes et performantes adaptées à vos besoins spécifiques."
            buttonText="Découvrir nos services"
            buttonHref="/services"
            titleColor="#F9FAFB"
            textColor="#D1D5DB"
            buttonColor="#EF4444"
          />
        </section>

        {/* Exemple 8: Style moderne */}
        <section style={{ marginBottom: '4rem' }}>
          <div className="container">
            <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
              8. Style moderne (Orange)
            </h2>
          </div>
          <BasicCenterCTA 
            backgroundColor="#EA580C"
            maskPosition="top"
            maskColor="#C2410C"
            titleColor="#FFFFFF"
            textColor="#FED7AA"
            buttonColor="#1F2937"
            title="Innovation & Créativité"
            text="Nous repoussons les limites du possible pour créer des expériences utilisateur exceptionnelles."
            buttonText="Voir nos réalisations"
          />
        </section>

        <div style={{ height: '4rem' }}></div>
      </div>
    </main>
  );
}
