"use client";

import Hero4 from '@/components/Heros/Hero4';
import Hero3 from '@/components/Heros/Hero3';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import Values from '@/components/Values';
import TitleTextImages from '@/components/TitleTextImages';
import Description from '@/components/Description';
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';


export default function AgencyClient({ params }) {
  const locale = params.locale || 'fr';

  // Données des expertises (en attendant une meilleure solution de traduction)
  const expertisesData = locale === 'fr' ? [
    {
      title: "Création et refonte d'identité",
      items: [
        "Conception de logotypes",
        "Création de chartes graphiques",
        "Design de marchandises",
        "Impression sur-mesure"
      ]
    },
    {
      title: "Stratégie et design thinking",
      items: [
        "Ateliers UX collaboratifs",
        "Analyse concurrentielle",
        "Définition de personas",
        "Optimisation du parcours utilisateur"
      ]
    },
    {
      title: "Développement web",
      items: [
        "Sites vitrine",
        "Boutiques e-commerce",
        "Applications web et portails interactifs",
        "Intégration de systèmes tiers",
        "Optimisation de performance",
        "Référencement naturel"
      ]
    }
  ] : [
    {
      title: "Brand identity creation and redesign",
      items: [
        "Logo creation",
        "Merchandise design",
        "Custom printing"
      ]
    },
    {
      title: "Strategy and design thinking",
      items: [
        "Competitive analysis",
        "Persona definition",
        "User journey"
      ]
    },
    {
      title: "Web development",
      items: [
        "Showcase websites",
        "E-commerce",
        "Web applications"
      ]
    }
  ];

  return (
    <div>
      <Hero4 
        locale={locale}
        imgSrc="/images/lucas-joliveau-siege-ordinateur-portable.png"
      />
      <div className="container">
              <Hero3
                label="À propos"
                title="Nous sommes une agence de design et de web basée en Rive-Sud de Montréal."
                description="Kapreon, c’est l’opposé des grosses agences où vous devenez un ticket dans un tableau. Ici, chaque projet est porté par la personne qui l’imagine et le réalise, épaulée par des collaborateurs québécois passionnés et engagés."
                buttonText="Découvrir nos projets"
                buttonLink="https://g.page/r/CdYePvCaFA87EAE/review"
              />
              </div>
            <StatsCards />
      <TitleTextImages locale={locale} />
      <Description
        descriptionTitle="Un lien tissé serré avec nos clients"
        descriptionText={
          <>
            De la première prise de contact jusqu'à la livraison, Lucas, le fondateur de l'agence est votre point de contact unique.
            <br /><br />
            Quand un projet le demande, Kapreon collabore avec des partenaires locaux basés au Québec, que nous connaissons et en qui nous avons confiance.
            <br /><br />
            Contrairement aux grosses agences québécoises, avec Kapreon vous n’avez aucun intermédiaire. Ça signifie plus de clarté, des processus ultra fluides, et une grande réactivité tout au long de l’exécution du projet.
          </>
        }
        showButton={false}
        titleTag="h3"
      />


      <ThreeColumnTitle locale={locale} />
      <Values />
      <Testimonials locale={locale} />
      <ExpertiseAccordion
        title={locale === 'fr' ? 'Nos expertises' : 'Our expertise'}
        expertises={expertisesData}
      />
<BasicCenterCTA
  backgroundColor="#C8CEC9"
  titleColor="#19271B"
  textColor="#19271B"
  title="Nous réglons vos problèmes grâce au design."
  text="Chaque projet est suivi de bout en bout, avec une communication claire et des délais respectés."
  buttonText="Démarrer un projet"
  buttonHref={`/${locale}/contact`}
/>
    </div>
  );
}
