.roundedButton {
    border-radius: 3em;
    border: 1px solid rgba(28, 29, 32, 0.175);
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s linear;
  
    p {
      position: relative;
      z-index: 1;
      transition: color 0.1s linear;
      width: max-content;
    }
  
    &:hover:not(.active) {
      p,
      .count {
        color: #19271B;
        transition: color 0.1s linear;
      }
    }
  
    &.active {
      background-color: #19271B;
      color: white;
  
      .content {
        color: white;
      }
    }
  }
  
  .circle {
    width: 100%;
    height: 150%;
    position: absolute;
    border-radius: 50%;
    top: 100%;
    transition: background-color 0.3s ease;
  
    .active & {
      background-color: #19271B !important;
    }
  }
  
  .count {
    display: inline-block;
    margin-left: 0.7em;
    opacity: 0.6;
    font-size: 0.7em;
    position: absolute;
    right: 1.9rem;
    top: 35%;
    transform: translateY(-50%);
    transition: color 0.1s linear;
  }

  // Variants de styles prédéfinis
  &.primary {
    color: #ffffff;

    .circle {
      background-color: #3B82F6;
    }

    &:hover:not(.active) {
      p, .count {
        color: #ffffff;
      }
    }
  }

  &.secondary {
    color: #374151;

    .circle {
      background-color: #E5E7EB;
    }

    &:hover:not(.active) {
      p, .count {
        color: #374151;
      }
    }
  }

  &.dark {
    color: #ffffff;

    .circle {
      background-color: #1F2937;
    }

    &:hover:not(.active) {
      p, .count {
        color: #ffffff;
      }
    }
  }

  &.light {
    color: #1F2937;

    .circle {
      background-color: #F9FAFB;
    }

    &:hover:not(.active) {
      p, .count {
        color: #1F2937;
      }
    }
  }

  // Variant par défaut (garde le comportement actuel)
  &.default {
    color: inherit;
  }
  
  .content {
    display: flex;
    gap: 0.5em;
    position: relative;
    z-index: 1;
    transition: color 0.1s linear;
    padding: 0.5rem 2.5rem;
  }
  
  .btn-icon {
    p {
      width: 20px;
    }
  }
  
  .text {
    position: relative;
    z-index: 2;
    display: inline-block;
  }
