import React, { useEffect, useRef, useState } from "react";
import styles from "./style.module.scss";
import gsap from "gsap";
import Magnetic from "../Magnetic";

export default function RoundedButton({
  children,
  backgroundColor = "#FFA3B2",
  textColor = null,
  variant = "default", // "default", "primary", "secondary", "dark", "light", "custom"
  count,
  isActive = false,
  onClick,
  exclusive = false,
  className = "",
  style = {},
  ...attributes
}) {
  const [isTouch, setIsTouch] = useState(false);           // ⬅️ détection mobile

  const containerRef = useRef(null);
  const circle = useRef(null);
  const timeline = useRef(null);
  const timeoutId = useRef(null);
  const containerPrevActive = useRef(isActive);
  const circlePrevActive = useRef(isActive);

  /* ----- détection tactile ----- */
  useEffect(() => {
    const touch =
      typeof window !== "undefined" &&
      (("ontouchstart" in window) ||
        navigator.maxTouchPoints > 0 ||
        window.matchMedia("(pointer: coarse)").matches);
    setIsTouch(touch);
  }, []);

  /* ----- animations (desktop seulement) ----- */
  useEffect(() => {
    if (isTouch) return;                                     // ⬅️ on saute tout
    if (containerRef.current) {
      if (containerPrevActive.current && !isActive) {
        gsap.to(containerRef.current, {
          backgroundColor: "transparent",
          duration: 0.4,
          ease: "power3.out",
        });
      } else if (!containerPrevActive.current && isActive) {
        gsap.to(containerRef.current, {
          backgroundColor: "#19271B",
          duration: 0.4,
          ease: "power3.out",
        });
      }
    }
    containerPrevActive.current = isActive;
  }, [isActive, isTouch]);

  useEffect(() => {
    if (isTouch || !circle.current) return;
    timeline.current = gsap.timeline({ paused: true });
    timeline.current
      .to(
        circle.current,
        { top: "-25%", width: "150%", duration: 0.4, ease: "expoScale(0.5,7,none)" },
        "enter"
      )
      .to(circle.current, { top: "-150%", width: "125%", duration: 0.25 }, "exit");
  }, [isTouch]);

  useEffect(() => {
    if (isTouch || !circle.current) return;
    if (circlePrevActive.current && !isActive) {
      gsap.set(circle.current, { top: "100%", width: "100%" });
      timeline.current = gsap.timeline({ paused: true });
      timeline.current
        .to(
          circle.current,
          { top: "-25%", width: "150%", duration: 0.4, ease: "expoScale(0.5,7,none)" },
          "enter"
        )
        .to(circle.current, { top: "-150%", width: "125%", duration: 0.25 }, "exit");
    }
    if (!circlePrevActive.current && isActive) {
      if (timeline.current) timeline.current.kill();
      gsap.killTweensOf(circle.current);
      gsap.set(circle.current, { top: "100%", width: "100%" });
    }
    circlePrevActive.current = isActive;
  }, [isActive, isTouch]);

  useEffect(() => {
    return () => {
      if (timeoutId.current) clearTimeout(timeoutId.current);
      if (timeline.current) timeline.current.kill();
    };
  }, []);

  /* ----- handlers (desktop seulement) ----- */
  const manageMouseEnter = () => {
    if (isTouch || isActive) return;
    if (timeoutId.current) clearTimeout(timeoutId.current);
    if (timeline.current) timeline.current.tweenFromTo("enter", "exit");
  };

  const manageMouseLeave = () => {
    if (isTouch || isActive) return;
    timeoutId.current = setTimeout(() => {
      if (timeline.current) timeline.current.play();
    }, 300);
  };

  const handleClick = () => {
    if (onClick && typeof onClick === 'function') {
      onClick();
    }
  };

  // Définir les couleurs de fond du cercle selon le variant
  const getCircleColor = () => {
    const circleColors = {
      default: backgroundColor,
      primary: "#3B82F6",
      secondary: "#E5E7EB",
      dark: "#1F2937",
      light: "#F9FAFB",
      custom: backgroundColor
    };

    return circleColors[variant] || backgroundColor;
  };

  const circleColor = getCircleColor();

  /* ----- rendu ----- */
  // Pas de Magnetic sur mobile → on rend directement le <div>
  const ButtonBody = (
    <div
      ref={containerRef}
      className={`${styles.roundedButton} ${styles[variant]} ${isActive ? styles.active : ""} ${className}`}
      style={{
        overflow: "hidden",
        position: "relative",
        ...(variant === "custom" && textColor && { color: textColor }),
        ...style
      }}
      onMouseEnter={manageMouseEnter}
      onMouseLeave={manageMouseLeave}
      onClick={handleClick}
      {...attributes}
    >
      {isTouch ? (
        /* mobile : pas de Magnetic interne */
        <div className={styles.content}>
          <div className={styles.text}>{children}</div>
          {count !== undefined && <span className={styles.count}>{count}</span>}
        </div>
      ) : (
        /* desktop : Magnetic interne conservé */
        <Magnetic factor={0.2} targetRef={containerRef}>
          <div className={styles.content}>
            <div className={styles.text}>{children}</div>
            {count !== undefined && <span className={styles.count}>{count}</span>}
          </div>
        </Magnetic>
      )}
      <div
        ref={circle}
        className={styles.circle}
        style={{
          backgroundColor: isActive ? "#19271B" : circleColor
        }}
      />
    </div>
  );

  return isTouch ? ButtonBody : <Magnetic>{ButtonBody}</Magnetic>;
}
